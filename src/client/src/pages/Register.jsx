import { useState } from 'react';
import { Link } from 'react-router-dom';
import Footer from "../components/Footer";
import NavBar from "../components/NavBar";

export default function Login() {
    const [pseudo, setPseudo] = useState('');
    const [password, setPassword] = useState('');
    const [isNarrator, setIsNarrator] = useState(true);

    const handleSubmit = (e) => {
        e.preventDefault();
        // Handle login logic here
        console.log('Login attempt:', { pseudo, password, isNarrator });
    };

    return (
        <>
            <NavBar />
            <main id="login_page">
                <section id="login">
                    <div className="login-card">
                        <h1>Se connecter</h1>

                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label htmlFor="pseudo">Pseudo</label>
                                <input
                                    type="text"
                                    id="pseudo"
                                    name="pseudo"
                                    placeholder="Pseudo"
                                    value={pseudo}
                                    onChange={(e) => setPseudo(e.target.value)}
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="password">Mot de passe</label>
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    placeholder="Mot de passe"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                />
                            </div>

                            <div className="form-group toggle-group">
                                <label htmlFor="narrator-toggle">Être narrateur</label>
                                <div className="toggle-switch">
                                    <input
                                        type="checkbox"
                                        id="narrator-toggle"
                                        checked={isNarrator}
                                        onChange={(e) => setIsNarrator(e.target.checked)}
                                    />
                                    <span className="slider"></span>
                                </div>
                            </div>

                            <button type="submit" className="confirm-button">
                                Confirmer
                            </button>
                            <Link to="/register" className="create-account-link">
                                Créer un compte
                            </Link>
                        </form>
                    </div>
                </section>
            </main>
            <Footer />
        </>
    );
}
